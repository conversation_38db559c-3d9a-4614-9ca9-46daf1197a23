#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra embedding API
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add libs to path
sys.path.append('libs')

# Import directly from the module file
import importlib.util
spec = importlib.util.spec_from_file_location("typesense_vector_db", "libs/typesense_vector_db.py")
typesense_module = importlib.util.module_from_spec(spec)

# Mock the relative import
class MockLLMRouter:
    @staticmethod
    def create_router_from_env():
        return None

# Add mock to sys.modules to handle the relative import
sys.modules['libs.llm_router'] = MockLLMRouter()

# Now load the module
spec.loader.exec_module(typesense_module)
CustomEmbeddings = typesense_module.CustomEmbeddings

def test_embeddings():
    """Test the custom embeddings implementation"""

    # Import get_env_var function
    from libs.common import get_env_var

    # Get environment variables
    embeddings_model = get_env_var('EMBEDDINGS_MODEL', 'text-embedding-ada-002')
    embeddings_api_base = get_env_var('EMBEDDINGS_API_BASE')
    embeddings_api_key = get_env_var('EMBEDDINGS_API_KEY')
    
    print(f"🔧 Testing embeddings configuration:")
    print(f"   Model: {embeddings_model}")
    print(f"   API Base: {embeddings_api_base}")
    print(f"   API Key: {'***' + embeddings_api_key[-4:] if embeddings_api_key else 'Not set'}")
    
    if not embeddings_api_base or not embeddings_api_key:
        print("❌ Missing required environment variables:")
        print("   - EMBEDDINGS_API_BASE")
        print("   - EMBEDDINGS_API_KEY")
        return False
    
    try:
        # Initialize custom embeddings
        embeddings = CustomEmbeddings(
            model=embeddings_model,
            api_base=embeddings_api_base,
            api_key=embeddings_api_key
        )
        
        # Test with a simple query
        test_text = "Hello, this is a test query for embedding."
        print(f"\n🧪 Testing with text: '{test_text}'")
        
        # Create embedding
        embedding = embeddings.embed_query(test_text)
        
        print(f"✅ Embedding created successfully!")
        print(f"   Dimension: {len(embedding)}")
        print(f"   First 5 values: {embedding[:5]}")
        print(f"   Last 5 values: {embedding[-5:]}")
        
        # Test with multiple documents
        test_texts = [
            "This is the first document.",
            "This is the second document.",
            "This is the third document."
        ]
        
        print(f"\n🧪 Testing with multiple documents...")
        embeddings_list = embeddings.embed_documents(test_texts)
        
        print(f"✅ Multiple embeddings created successfully!")
        print(f"   Number of embeddings: {len(embeddings_list)}")
        for i, emb in enumerate(embeddings_list):
            print(f"   Document {i+1}: dimension {len(emb)}, first 3 values: {emb[:3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing embeddings: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting embedding API test...")
    success = test_embeddings()
    
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Tests failed!")
        sys.exit(1)
